<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.pi.SqglSqczDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
      -->
    
    <select id="getTimeOutHandle" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            t1.id,
            t1.sqdj_id,
            t1.handle_post_code,
            t1.add_time,
            t1.org_code,
            t2.event_name,
            t3.alarm_type,
            t2.area_name,
            t2.status
        FROM
            acp_pi_sqgl_sqcz t1
                LEFT JOIN acp_pi_sqgl_sqdj t2 ON t1.sqdj_id = t2.id
                LEFT JOIN acp_pi_sqgl_mbpz t3 on t2.event_template_id = t3.id
        WHERE
            t1.status = '0'
          AND t2.status IN ( '0', '1', '2' )
          AND t1.is_del = 0
          AND t2.is_del = 0
    </select>

</mapper>
