<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.pi.AidedPatrolRecordDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="getDeviceRepairRegList" resultType="com.alibaba.fastjson.JSONObject">
        SELECT a.*
        FROM pam_device_repair_reg a
        WHERE a.is_del = 0
          AND a.applicant_time >= #{startTime}
          AND a.applicant_time &lt;= #{endTime}
          AND a.org_code = #{orgCode}
          <if test="patrolAreaId != null">
          AND EXISTS( SELECT 1 FROM acp_pi_patrol_area_mapping WHERE patrol_area_id = #{patrolAreaId} AND mapping_area_id = a.room_id )
          </if>
        ORDER BY a.applicant_time DESC
    </select>

    <select id="getMedicalAppointmentList" resultType="com.alibaba.fastjson.JSONObject">
        SELECT a.*,list.jsh,list.room_name FROM ihc_ipm_internal_medical_appointment a
        INNER JOIN vw_acp_pm_prisoner_List list on a.supervised_user_code = list.jgrybm
        where  a.is_del = 0
        AND a.disease_time >= #{startTime}
        AND a.disease_time &lt;= #{endTime}
        AND list.org_code = #{orgCode}
        <if test="patrolAreaId != null">
            AND EXISTS(SELECT 1 FROM acp_pi_patrol_area_mapping WHERE patrol_area_id = #{patrolAreaId} AND mapping_area_id= list.jsh )
        </if>
    </select>

    <select id="getZyryNum" resultType="java.lang.Integer">
        SELECT
        COUNT(1) FILTER (WHERE ryzt='10') AS nums
        FROM vw_acp_pm_prisoner_list list
        WHERE org_code=#{orgCode}
          and EXISTS(SELECT 1 FROM acp_pi_patrol_area_mapping
                              WHERE org_code=#{orgCode} and patrol_area_id=#{patrolAreaId} and mapping_type='room' and is_del='0' and mapping_area_id=jsh)
        AND list.rssj &lt;= #{startTime}  AND ( list.cssj >= #{endTime} OR list.cssj IS NULL )

    </select>
    <select id="getPersonDistribution" resultType="com.alibaba.fastjson.JSONObject">
        SELECT * FROM (
          SELECT
              COUNT(1) AS nums,'totalPerson' AS businessType,'总人数' AS businessTypeName,1 AS indexnum
          FROM vw_acp_pm_prisoner_list list
          WHERE list.org_code=#{orgCode} and EXISTS(SELECT 1 FROM acp_pi_patrol_area_mapping WHERE org_code=#{orgCode} and patrol_area_id=#{patrolAreaId} and mapping_type='room' and is_del='0' and mapping_area_id=jsh)
            AND list.rssj &lt;= #{startTime}  AND ( list.cssj >= #{endTime} OR list.cssj IS NULL )
          UNION ALL
          SELECT
              COUNT(1) FILTER (WHERE ryzt='10') AS nums,'detainedPerson' AS businessType,'在押' AS businessTypeName,2 AS indexnum
          FROM vw_acp_pm_prisoner_list list
          WHERE list.org_code=#{orgCode} and EXISTS(SELECT 1 FROM acp_pi_patrol_area_mapping WHERE org_code=#{orgCode} and patrol_area_id=#{patrolAreaId} and mapping_type='room' and is_del='0' and mapping_area_id=jsh)
            AND list.rssj &lt;= #{startTime}  AND ( list.cssj >= #{endTime} OR list.cssj IS NULL )
          UNION ALL
          SELECT
              COUNT(1) AS nums, 'medicalTreatment' AS businessType, '出所就医' AS businessTypeName,3 AS indexnum
          FROM acp_pm_out_prison_treatment a
                   LEFT JOIN vw_acp_pm_prisoner_list list ON a.jgrybm = list.jgrybm
          WHERE a.is_del=0 AND a.out_time &lt;=#{endTime} AND list.org_code=#{orgCode}
            AND list.rssj &lt;= #{startTime}  AND ( list.cssj >= #{endTime} OR list.cssj IS NULL )
            and EXISTS(SELECT 1 FROM acp_pi_patrol_area_mapping WHERE org_code=#{orgCode} and patrol_area_id=#{patrolAreaId} and mapping_type='room' and is_del='0' and mapping_area_id=list.jsh)
          UNION ALL
          SELECT
              COUNT(1) FILTER (WHERE CURRENT_DATE - csrq::date &lt; 18*365) AS nums,'minorPerson' AS businessType,'未成年' AS businessTypeName,4 AS indexnum
          FROM vw_acp_pm_prisoner_list list
          WHERE list.org_code=#{orgCode} and EXISTS(SELECT 1 FROM acp_pi_patrol_area_mapping WHERE org_code=#{orgCode} and patrol_area_id=#{patrolAreaId} and mapping_type='room' and is_del='0' and mapping_area_id=jsh)
            AND list.rssj &lt;= #{startTime}  AND ( list.cssj >= #{endTime} OR list.cssj IS NULL )
          UNION ALL
          SELECT
              COUNT(1) AS nums,'focusPerson' AS businessType,'重点关注' AS businessTypeName,5 AS indexnum
          FROM pam_attention_prisoner a INNER JOIN vw_acp_pm_prisoner_list list on a.jgrybm = list.jgrybm
          WHERE a.is_del = 0 and a.reg_status = '3'
            AND list.org_code=#{orgCode} and EXISTS(SELECT 1 FROM acp_pi_patrol_area_mapping WHERE org_code=#{orgCode} and patrol_area_id=#{patrolAreaId} and mapping_type='room' and is_del='0' and mapping_area_id=jsh)
            AND list.rssj &lt;= #{startTime}  AND ( list.cssj >= #{endTime} OR list.cssj IS NULL )
          UNION ALL
          SELECT
              COUNT(1) AS nums, 'restraintUse' AS businessType, '戒具加戴' AS businessTypeName,6 AS indexnum
          FROM acp_gj_equipment_use t
                   INNER JOIN vw_acp_pm_prisoner_list list ON list.is_del = 0 AND t.jgrybm = list.jgrybm
          WHERE t.is_del = 0 AND t.status IN ('07','04','03','06')
            AND t.add_time >= #{startTime} AND t.add_time &lt;= #{endTime}
            AND list.rssj &lt;= #{startTime}  AND ( list.cssj >= #{endTime} OR list.cssj IS NULL )
            AND list.org_code=#{orgCode} and EXISTS(SELECT 1 FROM acp_pi_patrol_area_mapping WHERE org_code=#{orgCode} and patrol_area_id=#{patrolAreaId} and mapping_type='room' and is_del='0' and mapping_area_id=list.jsh)
          UNION ALL
          SELECT
              COUNT(1) FILTER (WHERE gj NOT IN('156','158','344','446')) AS nums,'foreignPerson' AS businessType,'外籍' AS businessTypeName,8 AS indexnum
          FROM vw_acp_pm_prisoner_list list
          WHERE list.org_code=#{orgCode} and EXISTS(SELECT 1 FROM acp_pi_patrol_area_mapping WHERE org_code=#{orgCode} and patrol_area_id=#{patrolAreaId} and mapping_type='room' and is_del='0' and mapping_area_id=jsh)
            AND list.rssj &lt;= #{startTime}  AND ( list.cssj >= #{endTime} OR list.cssj IS NULL )
          UNION ALL
          SELECT
              COUNT(1) FILTER (WHERE sfsm='0') AS nums,'confidentialPerson' AS businessType,'涉密' AS businessTypeName,7 AS indexnum
          FROM vw_acp_pm_prisoner_list list
          WHERE list.org_code=#{orgCode} and EXISTS(SELECT 1 FROM acp_pi_patrol_area_mapping WHERE org_code=#{orgCode} and patrol_area_id=#{patrolAreaId} and mapping_type='room' and is_del='0' and mapping_area_id=jsh)
            AND list.rssj &lt;= #{startTime}  AND ( list.cssj >= #{endTime} OR list.cssj IS NULL )

      ) t
    </select>
</mapper>
