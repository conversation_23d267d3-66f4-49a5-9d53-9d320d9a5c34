<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.sldxxfb.SldxxfbDao">
    <!-- 查询总数及 各所昨日(指定日期 ymd)羁押人数-->
    <select id="getAllPrisonerCount" resultType="java.util.Map">

        SELECT
        COALESCE(org_code, '') AS orgCode,
        COUNT(*) AS nowTotalNum,
        SUM(CASE WHEN rssj &lt; #{endDate} THEN 1 ELSE 0 END) AS historyTotalNum
        FROM vw_acp_pm_prisoner_list
        GROUP BY GROUPING SETS (
        (org_code),
        ()
        )
    </select>
    <!-- 查询近一年 按月分组羁押人数 -->
    <select id="getAllPrisonerInOneYear" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        TO_CHAR(rssj, 'YYYY-MM') AS month,
        COUNT(*) AS count
        FROM vw_acp_pm_prisoner_list
        WHERE rssj is not null and rssj >= DATE_TRUNC('year', CURRENT_DATE) - INTERVAL '1 year'
            AND rssj &lt;= CURRENT_DATE
        GROUP BY TO_CHAR(rssj, 'YYYY-MM')
        ORDER BY month;
    </select>
    <select id="getAllPrisonerOutOneYear" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            TO_CHAR(cssj, 'YYYY-MM') AS month,
        COUNT(*) AS count
        FROM vw_acp_pm_prisoner_list
        WHERE cssj is not null and cssj >= DATE_TRUNC('year', CURRENT_DATE) - INTERVAL '1 year'
          AND cssj &lt;= CURRENT_DATE
        GROUP BY TO_CHAR(cssj, 'YYYY-MM')
        ORDER BY month;
    </select>
    <!-- 查询 今日，昨日 今年,当前羁押总人数,昨日羁押总人数 -->
    <select id="getInCount" resultType="java.util.Map">

        SELECT
            COUNT(*) FILTER (WHERE rssj::date = CURRENT_DATE) AS todaycount,
            COUNT(*) FILTER (WHERE rssj::date = CURRENT_DATE - INTERVAL '1 day') AS yesterdaycount,
            COUNT(*) FILTER (WHERE EXTRACT(YEAR FROM rssj) = EXTRACT(YEAR FROM CURRENT_DATE)) AS yearcount,
            COUNT(*) AS nowtotalcount,
            SUM(CASE WHEN rssj &lt; CURRENT_DATE THEN 1 ELSE 0 END) AS yesterdaytotalcount
        FROM vw_acp_pm_prisoner_list where rssj is not null
        <if test="orgCode != null">
            and org_code = #{orgCode}
        </if>
    </select>
    <!-- 查询 各监所设计关押量总数 -->
    <select id="getAllPrisonerDesignAmount" resultType="com.alibaba.fastjson.JSONObject">
        select
            org_code AS orgcode,
            sum(plan_imprisonment_amount) as amountcount
        from acp_pm_area_prison_room
        where status='0' and is_del=0
        group by org_code
    </select>

    <!-- 查询 今日，昨日 今年,当前出所总人数,昨日出所总人数 -->
    <select id="getOutCount" resultType="java.util.Map">

        SELECT
        COUNT(*) FILTER (WHERE cssj::date = CURRENT_DATE) AS todaycount,
        COUNT(*) FILTER (WHERE cssj::date = CURRENT_DATE - INTERVAL '1 day') AS yesterdaycount,
        COUNT(*) FILTER (WHERE EXTRACT(YEAR FROM cssj) = EXTRACT(YEAR FROM CURRENT_DATE)) AS yearcount,
        COUNT(*) AS nowtotalcount,
        SUM(CASE WHEN cssj &lt; CURRENT_DATE THEN 1 ELSE 0 END) AS yesterdaytotalcount
        FROM vw_acp_pm_prisoner_list where cssj is not null
        <if test="orgCode != null">
            and org_code = #{orgCode}
        </if>
    </select>

    <select id="ssjd" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            org_code,
            SUM(CASE WHEN t.sshj IN ('11', '12', '13', '14') THEN t.s_num ELSE 0 END) AS ga_total,
            jsonb_agg (CASE WHEN t.sshj IN ('11', '12', '13', '14') THEN jsonb_build_object ( 'sshjCode', t.sshj, 'num', t.s_num ) ELSE '{}' END) AS ga_total_detail,
            SUM(CASE WHEN t.sshj IN ('21', '22', '23', '24' ) THEN t.s_num ELSE 0 END) AS jcy_total,
            jsonb_agg (CASE WHEN t.sshj IN ('21', '22', '23', '24') THEN jsonb_build_object ( 'sshjCode', t.sshj, 'num', t.s_num ) ELSE '{}' END) AS jcy_total_detail,
            SUM(CASE WHEN t.sshj IN ('31', '32', '33', '34', '35', '36', '37', '38') THEN t.s_num ELSE 0 END) AS fy_total,
            jsonb_agg (CASE WHEN t.sshj IN ('31', '32', '33', '34', '35', '36', '37', '38') THEN jsonb_build_object ( 'sshjCode', t.sshj, 'num', t.s_num ) ELSE '{}' END) AS fy_total_detail,
            SUM(CASE WHEN t.sshj IN ('99', '41', '42', '43', '44') THEN t.s_num ELSE 0 END) AS qt_total,
            jsonb_agg (CASE WHEN t.sshj IN ('99', '41', '42', '43', '44') THEN jsonb_build_object ( 'sshjCode', t.sshj, 'num', t.s_num ) ELSE '{}' END) AS qt_total_detail
        FROM
            (SELECT
                 v.org_code,
                 v.sshj,
                 COUNT (1) AS s_num
             FROM
                 vw_acp_pm_prisoner_kss v
             WHERE
                 v.sshj IS NOT NULL
               AND v.sshj != ''
			AND v.is_del = '0'
            <if test="orgCode != null">
                and org_code = #{orgCode}
            </if>
             GROUP BY
                 v.org_code,
                 v.sshj
             ORDER BY v.org_code, v.sshj ) T
        GROUP BY
            T.org_code
    </select>

    <!--
    acp_gj_risk_assmt
    zs: 风险总数 rl1: 一级风险数  rl2: 二级风险数  rl3: 三级风险数
    -->
    <select id="getFxryfb" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        count(1) as zs,
        sum(CASE WHEN a.fxdj='1' THEN 1 ELSE 0 END ) as rl1,
        sum(CASE WHEN a.fxdj='2' THEN 1 ELSE 0 END ) as rl2,
        sum(CASE WHEN a.fxdj='3' THEN 1 ELSE 0 END ) as rl3
        FROM vw_acp_pm_prisoner_in a  where a.is_del=0 and a.fxdj in ('1','2','3')
        <choose>
            <when test="codeType=='01'">
                and a.jsh = #{code}
            </when>
            <when test="codeType=='02'">
                and a.area_id = #{code}
            </when>
            <otherwise>
                and a.org_code = #{code}
            </otherwise>
        </choose>

    </select>

    <select id="getWggjPage" parameterType="string" resultType="com.rs.module.acp.controller.admin.sldxxfb.vo.WgdjVO">
        SELECT a.*, b.area_id FROM "acp_pi_violation_record" a
        left join acp_pm_area_prison_room b on a.room_id = b.id
        where a.is_del = '0'
        <choose>
            <when test='timeRange=="1"'>
                and a.add_time &gt;= date_trunc('day', now())
            </when>
            <when test='timeRange=="2"'>
                and a.add_time &gt;= date_trunc('week', now())
            </when>
            <when test='timeRange=="3"'>
                and a.add_time &gt;= date_trunc('month', now())
            </when>
            <otherwise>
                and a.add_time &gt;= date_trunc('year', now())
            </otherwise>
        </choose>
        <choose>
            <when test='handleStatus=="1"'>
                and  a.status in ('03','04')
            </when>
            <when test='handleStatus=="2"'>
                and a.status in ('04')
            </when>
            <otherwise>
                and a.status in ('03')
            </otherwise>
        </choose>

        <choose>
            <when test="codeType=='01'">
                and a.room_id = #{code}
            </when>
            <when test="codeType=='02'">
                and b.area_id = #{code}
            </when>
            <otherwise>
                and a.org_code = #{code}
            </otherwise>
        </choose>
        order by a.add_time desc
    </select>

    <select id="getWggjAllCount" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        SELECT count(1) as allCount,
        sum(case when a.status = '03' then 1 else 0 end) as noHandleCount,
        sum(case when a.status = '04' then 1 else 0 end) as hasHandleCount
        FROM "acp_pi_violation_record" a left join acp_pm_area_prison_room b on a.room_id = b.id
        where a.is_del = '0' and a.status in ('03','04')
        <choose>
            <when test='timeRange=="1"'>
                and a.add_time &gt;= date_trunc('day', now())
            </when>
            <when test='timeRange=="2"'>
                and a.add_time &gt;= date_trunc('week', now())
            </when>
            <when test='timeRange=="3"'>
                and a.add_time &gt;= date_trunc('month', now())
            </when>
            <otherwise>
                and a.add_time &gt;= date_trunc('year', now())
            </otherwise>
        </choose>
        <choose>
            <when test="codeType=='01'">
                and a.room_id = #{code}
            </when>
            <when test="codeType=='02'">
                and b.area_id = #{code}
            </when>
            <otherwise>
                and a.org_code = #{code}
            </otherwise>
        </choose>
    </select>

    <select id="getRoomMonitorIndex" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        COUNT(1) AS zs,
        SUM(CASE WHEN a.fxdj='1' THEN 1 ELSE 0 END) AS rl1,
        SUM(CASE WHEN a.fxdj='2' THEN 1 ELSE 0 END) AS rl2,
        SUM(CASE WHEN a.fxdj='3' THEN 1 ELSE 0 END) AS rl3,
        SUM(CASE WHEN EXISTS (
        SELECT 1 FROM acp_gj_equipment_use t
        WHERE t.jgrybm = a.jgrybm
        AND t.is_del = 0
        AND t.status NOT IN ('01', '02', '09', '05')
        AND t.actual_end_time &lt;= CURRENT_DATE
        ) THEN 1 ELSE 0 END) AS equipment_users,
        SUM(CASE WHEN a.rssj >= CURRENT_DATE - INTERVAL '3 day' THEN 1 ELSE 0 END) AS rssj_count,
        JSON_AGG(
        CASE WHEN a.fxdj='1' THEN
        JSON_BUILD_OBJECT('jgrybm', a.jgrybm, 'xm', a.xm)
        ELSE NULL END
        ) FILTER (WHERE a.fxdj='1') AS rl1_json,
        JSON_AGG(
        CASE WHEN a.fxdj='2' THEN
        JSON_BUILD_OBJECT('jgrybm', a.jgrybm, 'xm', a.xm)
        ELSE NULL END
        ) FILTER (WHERE a.fxdj='2') AS rl2_json,
        JSON_AGG(
        CASE WHEN a.fxdj='3' THEN
        JSON_BUILD_OBJECT('jgrybm', a.jgrybm, 'xm', a.xm)
        ELSE NULL END
        ) FILTER (WHERE a.fxdj='3') AS rl3_json,
        JSON_AGG(
        CASE WHEN EXISTS (
        SELECT 1 FROM acp_gj_equipment_use t
        WHERE t.jgrybm = a.jgrybm
        AND t.is_del = 0
        AND t.status in ('07','04','03','06')
        --  AND t.actual_end_time &lt;= CURRENT_DATE
        ) THEN JSON_BUILD_OBJECT('jgrybm', a.jgrybm, 'xm', a.xm) ELSE NULL END
        ) FILTER (WHERE EXISTS (
        SELECT 1 FROM acp_gj_equipment_use t
        WHERE t.jgrybm = a.jgrybm
        AND t.is_del = 0
        AND t.status in ('07','04','03','06')
        -- AND t.actual_end_time &lt;= CURRENT_DATE
        )) AS equipment_users_json,
        JSON_AGG(
        CASE WHEN a.rssj >= CURRENT_DATE - INTERVAL '3 day' THEN
        JSON_BUILD_OBJECT('jgrybm', a.jgrybm, 'xm', a.xm, 'rssj', a.rssj)
        ELSE NULL END
        ) FILTER (WHERE a.rssj >= CURRENT_DATE - INTERVAL '3 day') AS rssj_json
        FROM
        vw_acp_pm_prisoner_in a
        WHERE a.is_del=0
        AND a.jsh = #{roomId}
        AND a.org_code = #{orgCode}

    </select>
</mapper>
