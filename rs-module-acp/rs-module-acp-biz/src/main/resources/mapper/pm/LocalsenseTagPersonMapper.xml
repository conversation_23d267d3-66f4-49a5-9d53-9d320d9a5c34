<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.pm.LocalsenseTagPersonDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <!-- 查询标签人员及房间信息 -->
    <select id="selectTagPersonWithRoomInfo" resultType="com.rs.module.acp.controller.admin.pm.vo.LocalsenseTagPersonRespVO">
        SELECT t1.*, t2.jsh AS roomId, t2.room_name AS roomName
        FROM acp_pm_localsense_tag_person t1
                 INNER JOIN vw_acp_pm_prisoner_in t2 ON t1.bind_person_id = t2.jgrybm AND t1.status = '01'
        WHERE t1.is_del = 0
          AND t1.person_type = #{personType}
          AND t2.org_code = #{orgCode}
          AND t2.jsh = #{roomId}
    </select>

    <!-- 统计各房间绑定数量 -->
    <select id="selectTagJgryCountByRoom" resultType="map">
        SELECT t2.jsh AS roomId,COUNT(1) AS nums
        FROM acp_pm_localsense_tag_person t1
                 INNER JOIN vw_acp_pm_prisoner_in t2 ON t1.bind_person_id = t2.jgrybm AND t1.status = '01'
        WHERE t1.is_del = 0
          AND t1.person_type = #{personType}
          AND t2.org_code = #{orgCode}
          <if test="roomId != null and roomId != ''">
              AND t2.jsh = #{roomId}
            </if>
        GROUP BY t2.jsh
    </select>
    <select id="selectTagPersonCountByRoom" resultType="map">
        SELECT t2.jsh AS roomId,COUNT(1) AS nums
        FROM acp_pm_localsense_tag_person t1
        INNER JOIN vw_acp_pm_prisoner_in t2 ON t1.bind_person_id = t2.jgrybm AND t1.status = '01'
        WHERE t1.is_del = 0
          AND t1.person_type = '02'
          AND t2.org_code = #{orgCode}
        <if test="roomId != null and roomId != ''">
                AND t2.jsh = #{roomId}
            </if>
        GROUP BY t2.jsh
    </select>
    <update id="updateTagSetInfo">
        update acp_pm_localsense_tag_person
        set
        <if test="form.bloodOxygenTaskId != null and form.bloodOxygenTaskId != ''">
            blood_oxygen_task_id = #{form.bloodOxygenTaskId},
            blood_oxygen_period = #{form.bloodOxygenPeriod},
        </if>
        <if test="form.heartRateTaskId != null and form.heartRateTaskId != ''">
            heart_rate_task_id = #{form.heartRateTaskId},
            heart_rate_period = #{form.heartRatePeriod},
        </if>
        <if test="form.temperatureTaskId != null and form.temperatureTaskId != ''">
            temperature_task_id = #{form.temperatureTaskId},
            temperature_period = #{form.temperaturePeriod},
        </if>
        update_time = now()
        where bind_record_id = #{form.bindRecordId} and status = '01' and is_del = 0 and tag_id = #{form.tagId}
    </update>
</mapper>
